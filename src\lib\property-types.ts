import { 
  Home, 
  Building, 
  Building2, 
  Utensils, 
  Briefcase,
  Users,
  House
} from "lucide-react"

export interface PropertyTypeConfig {
  id: string
  label: string
  icon: any
  description: string
  color: string
  bgColor: string
  hoverColor: string
}

export const PROPERTY_TYPES: PropertyTypeConfig[] = [
  {
    id: "all",
    label: "All Properties",
    icon: Building2,
    description: "View all available properties",
    color: "text-slate-600",
    bgColor: "bg-slate-100",
    hoverColor: "hover:bg-slate-200"
  },
  {
    id: "hostel",
    label: "Hostel",
    icon: Building,
    description: "Student hostels and dormitories",
    color: "text-emerald-600",
    bgColor: "bg-emerald-100",
    hoverColor: "hover:bg-emerald-200"
  },
  {
    id: "apartment",
    label: "Apartment",
    icon: Home,
    description: "Furnished apartments for students",
    color: "text-blue-600",
    bgColor: "bg-blue-100",
    hoverColor: "hover:bg-blue-200"
  },
  {
    id: "house",
    label: "House",
    icon: House,
    description: "Independent houses and villas",
    color: "text-purple-600",
    bgColor: "bg-purple-100",
    hoverColor: "hover:bg-purple-200"
  },
  {
    id: "office",
    label: "Office",
    icon: Briefcase,
    description: "Office spaces and co-working areas",
    color: "text-orange-600",
    bgColor: "bg-orange-100",
    hoverColor: "hover:bg-orange-200"
  },
  {
    id: "hostel-mess",
    label: "Hostel Mess",
    icon: Utensils,
    description: "Hostel with mess facilities",
    color: "text-red-600",
    bgColor: "bg-red-100",
    hoverColor: "hover:bg-red-200"
  },
  {
    id: "pg",
    label: "PG",
    icon: Users,
    description: "Paying guest accommodations",
    color: "text-indigo-600",
    bgColor: "bg-indigo-100",
    hoverColor: "hover:bg-indigo-200"
  },
  {
    id: "flat",
    label: "Flat",
    icon: Home,
    description: "Independent flats and units",
    color: "text-teal-600",
    bgColor: "bg-teal-100",
    hoverColor: "hover:bg-teal-200"
  }
]

// Helper function to get property type config by id
export const getPropertyTypeConfig = (id: string): PropertyTypeConfig | undefined => {
  return PROPERTY_TYPES.find(type => type.id === id)
}

// Helper function to get all property types except "all"
export const getFilterablePropertyTypes = (): PropertyTypeConfig[] => {
  return PROPERTY_TYPES.filter(type => type.id !== "all")
}

// Helper function to get property type label
export const getPropertyTypeLabel = (id: string): string => {
  const config = getPropertyTypeConfig(id)
  return config?.label || id
}
