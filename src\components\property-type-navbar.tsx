"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { PROPERTY_TYPES, PropertyTypeConfig } from "@/lib/property-types"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface PropertyTypeNavBarProps {
  activeType: string
  onTypeChange: (type: string) => void
  className?: string
  showCounts?: boolean
  propertyCounts?: Record<string, number>
}

export function PropertyTypeNavBar({ 
  activeType, 
  onTypeChange, 
  className = "",
  showCounts = false,
  propertyCounts = {}
}: PropertyTypeNavBarProps) {
  const [showScrollButtons, setShowScrollButtons] = useState(false)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(false)

  useEffect(() => {
    const checkScrollability = () => {
      const container = document.getElementById('property-type-scroll-container')
      if (container) {
        const hasOverflow = container.scrollWidth > container.clientWidth
        setShowScrollButtons(hasOverflow)
        setCanScrollLeft(container.scrollLeft > 0)
        setCanScrollRight(container.scrollLeft < container.scrollWidth - container.clientWidth)
      }
    }

    checkScrollability()
    window.addEventListener('resize', checkScrollability)
    
    return () => window.removeEventListener('resize', checkScrollability)
  }, [])

  const scrollLeft = () => {
    const container = document.getElementById('property-type-scroll-container')
    if (container) {
      container.scrollBy({ left: -200, behavior: 'smooth' })
      setTimeout(() => {
        setCanScrollLeft(container.scrollLeft > 0)
        setCanScrollRight(container.scrollLeft < container.scrollWidth - container.clientWidth)
      }, 300)
    }
  }

  const scrollRight = () => {
    const container = document.getElementById('property-type-scroll-container')
    if (container) {
      container.scrollBy({ left: 200, behavior: 'smooth' })
      setTimeout(() => {
        setCanScrollLeft(container.scrollLeft > 0)
        setCanScrollRight(container.scrollLeft < container.scrollWidth - container.clientWidth)
      }, 300)
    }
  }

  const handleTypeClick = (typeId: string) => {
    // If clicking the same type, toggle to "all"
    if (activeType === typeId && typeId !== "all") {
      onTypeChange("all")
    } else {
      onTypeChange(typeId)
    }
  }

  return (
    <div className={`relative bg-white border-b border-slate-200 backdrop-blur-sm ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="relative flex items-center py-3 sm:py-4">
          {/* Left Scroll Button */}
          {showScrollButtons && canScrollLeft && (
            <Button
              variant="ghost"
              size="sm"
              onClick={scrollLeft}
              className="absolute left-2 z-10 bg-white shadow-lg hover:bg-slate-50 rounded-full p-2 border border-slate-200 transition-all duration-200"
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
          )}

          {/* Property Type Items Container */}
          <div
            id="property-type-scroll-container"
            className="flex items-center space-x-1 sm:space-x-2 overflow-x-auto scrollbar-hide scroll-smooth px-8 sm:px-12 lg:px-0"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {PROPERTY_TYPES.map((type: PropertyTypeConfig) => {
              const isActive = activeType === type.id || (activeType === "" && type.id === "all")
              const Icon = type.icon
              const count = propertyCounts[type.id] || 0

              return (
                <Button
                  key={type.id}
                  variant="ghost"
                  onClick={() => handleTypeClick(type.id)}
                  className={`
                    flex items-center space-x-1.5 sm:space-x-2 px-3 sm:px-4 py-2 rounded-full whitespace-nowrap transition-all duration-200 text-sm sm:text-base relative
                    ${isActive
                      ? `${type.bgColor} ${type.color} shadow-md border border-current/20 scale-105 font-semibold`
                      : `text-slate-600 hover:text-slate-800 ${type.hoverColor} hover:shadow-sm hover:scale-102`
                    }
                  `}
                >
                  <Icon className="w-3.5 h-3.5 sm:w-4 sm:h-4 flex-shrink-0" />
                  <span className="font-medium hidden sm:inline">{type.label}</span>
                  <span className="font-medium sm:hidden text-xs">{type.label.split(' ')[0]}</span>
                  {showCounts && count > 0 && (
                    <Badge
                      variant="secondary"
                      className={`
                        ml-1 text-xs px-1.5 py-0.5 hidden sm:inline-flex
                        ${isActive
                          ? 'bg-white/80 text-current'
                          : 'bg-slate-100 text-slate-600'
                        }
                      `}
                    >
                      {count}
                    </Badge>
                  )}
                </Button>
              )
            })}
          </div>

          {/* Right Scroll Button */}
          {showScrollButtons && canScrollRight && (
            <Button
              variant="ghost"
              size="sm"
              onClick={scrollRight}
              className="absolute right-2 z-10 bg-white shadow-lg hover:bg-slate-50 rounded-full p-2 border border-slate-200 transition-all duration-200"
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
